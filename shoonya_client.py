"""
shoonya_client.py
A minimal, reusable wrapper around the Shoonya REST API.
"""

from __future__ import annotations

import json
import os
from datetime import datetime
from typing import Optional

import pandas as pd
from pyotp import TOTP

# Third-party import (assumes Noren<PERSON><PERSON> is installed)
from NorenRestApiPy.NorenApi import NorenApi


class ShoonyaClient:
    """
    High-level, OOP wrapper for the Shoonya trading API.

    Responsibilities
    ----------------
    1. Load credentials from JSON file.
    2. Perform two-factor login (TOTP).
    3. Persist the session token for later reuse.
    4. Expose the underlying `NorenApi` object for advanced calls.
    """

    # Class-level constants: endpoints do not change across instances
    REST_URL = "https://api.shoonya.com/NorenWClientTP/"
    WS_URL   = "wss://api.shoonya.com/NorenWSTP/"

    # ------------------------------------------------------------------ #
    # Construction & helpers
    # ------------------------------------------------------------------ #
    def __init__(self, cred_file: str = "Cread.json", token_file: str = "session_token.txt"):
        """
        Parameters
        ----------
        cred_file  : str
            Path to JSON file with user credentials.
        token_file : str
            Path where the susertoken will be cached after successful login.
        """
        self.cred_file  = cred_file
        self.token_file = token_file
        self._api       = None   # underlying NorenApi instance
        self._creds     = None   # parsed credentials dict

    # ------------------------------------------------------------------ #
    # Public API
    # ------------------------------------------------------------------ #
    def login(self) -> bool:
        """
        Attempt login with 2FA (TOTP). Returns True on success, else False.
        The internal `NorenApi` instance is available through the `.api` property.
        """
        self._load_credentials()
        self._instantiate_api()

        otp = TOTP(self._creds["totp"]).now().zfill(6)

        response = self._api.login(
            userid      = self._creds["user_id"],
            password    = self._creds["password"],
            twoFA       = otp,
            vendor_code = self._creds["vendor_code"],
            api_secret  = self._creds["app_key"],
            imei        = self._creds["imei"],
        )

        if response is None or "susertoken" not in response:
            print(f"[{datetime.now():%H:%M:%S}] Login failed -> {response}")
            return False

        print(f"[{datetime.now():%H:%M:%S}] Login successful")
        self._save_token(response["susertoken"])
        return True

    @property
    def api(self) -> Optional[NorenApi]:
        """
        Provide access to the underlying NorenApi instance after login.
        Returns None if login has not been performed or failed.
        """
        return self._api

    # ------------------------------------------------------------------ #
    # Private helpers
    # ------------------------------------------------------------------ #
    def _load_credentials(self) -> None:
        """Load and validate JSON credentials file."""
        try:
            with open(self.cred_file, "r") as fp:
                self._creds = json.load(fp)
        except (FileNotFoundError, json.JSONDecodeError, KeyError) as exc:
            raise RuntimeError(f"Credential file error: {exc}") from None

    def _instantiate_api(self) -> None:
        """Create the low-level NorenApi instance."""
        # Small inline subclass so we can still override __init__ if needed
        class _Api(NorenApi):
            def __init__(self_):
                super().__init__(host=self.REST_URL, websocket=self.WS_URL)

        self._api = _Api()

    def _save_token(self, token: str) -> None:
        """Persist the session token to disk."""
        try:
            with open(self.token_file, "w") as fp:
                fp.write(token)
        except OSError as exc:
            print(f"Warning: could not save token -> {exc}")


# ---------------------------------------------------------------------- #
# Entry-point (only runs when executed directly)
# ---------------------------------------------------------------------- #
if __name__ == "__main__":
    client = ShoonyaClient()
    if client.login():
        # Example usage: access underlying api
        api = client.api
        # api.place_order(...)  etc.